import { serve } from "https://deno.land/std@0.168.0/http/server.ts"
import { createClient } from 'https://esm.sh/@supabase/supabase-js@2'

const corsHeaders = {
  'Access-Control-Allow-Origin': '*',
  'Access-Control-Allow-Headers': 'authorization, x-client-info, apikey, content-type',
}

interface NotificationRequest {
  device_token: string
  device_type?: string
  title: string
  body: string
  data?: Record<string, any>
}

serve(async (req) => {
  // Handle CORS preflight requests
  if (req.method === 'OPTIONS') {
    return new Response('ok', { headers: corsHeaders })
  }

  try {
    // Get the authorization header
    const authHeader = req.headers.get('Authorization')
    if (!authHeader) {
      throw new Error('No authorization header')
    }

    // Create Supabase client
    const supabaseClient = createClient(
      Deno.env.get('SUPABASE_URL') ?? '',
      Deno.env.get('SUPABASE_ANON_KEY') ?? '',
      {
        global: {
          headers: { Authorization: authHeader },
        },
      }
    )

    // Verify the user is authenticated
    const { data: { user }, error: authError } = await supabaseClient.auth.getUser()
    if (authError || !user) {
      throw new Error('Unauthorized')
    }

    // Parse the request body
    const notificationData: NotificationRequest = await req.json()
    
    // Validate required fields
    if (!notificationData.device_token || !notificationData.title || !notificationData.body) {
      throw new Error('Missing required fields: device_token, title, body')
    }

    // Get Firebase server key from environment
    const firebaseServerKey = Deno.env.get('FIREBASE_SERVER_KEY')
    if (!firebaseServerKey) {
      throw new Error('Firebase server key not configured')
    }

    // Prepare FCM payload
    const fcmPayload = {
      to: notificationData.device_token,
      notification: {
        title: notificationData.title,
        body: notificationData.body,
        icon: 'ic_launcher',
        sound: 'default',
        click_action: 'FLUTTER_NOTIFICATION_CLICK',
      },
      data: notificationData.data || {},
      priority: 'high',
      content_available: true,
    }

    // Add platform-specific configurations
    if (notificationData.device_type === 'ios') {
      fcmPayload['apns'] = {
        payload: {
          aps: {
            alert: {
              title: notificationData.title,
              body: notificationData.body,
            },
            badge: 1,
            sound: 'default',
            'content-available': 1,
          },
        },
      }
    } else if (notificationData.device_type === 'android') {
      fcmPayload['android'] = {
        notification: {
          title: notificationData.title,
          body: notificationData.body,
          icon: 'ic_launcher',
          sound: 'default',
          channel_id: 'pulsemeet_messages',
        },
        priority: 'high',
        ttl: '86400s', // 24 hours
      }
    }

    // Send FCM notification
    const fcmResponse = await fetch('https://fcm.googleapis.com/fcm/send', {
      method: 'POST',
      headers: {
        'Authorization': `key=${firebaseServerKey}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(fcmPayload),
    })

    const fcmResult = await fcmResponse.json()

    if (!fcmResponse.ok) {
      console.error('FCM Error:', fcmResult)
      throw new Error(`FCM request failed: ${fcmResult.error || 'Unknown error'}`)
    }

    // Check if the notification was successful
    if (fcmResult.failure > 0) {
      console.error('FCM Failure:', fcmResult.results)
      
      // Handle invalid tokens
      if (fcmResult.results && fcmResult.results[0] && fcmResult.results[0].error) {
        const error = fcmResult.results[0].error
        if (error === 'InvalidRegistration' || error === 'NotRegistered') {
          // Mark the device token as inactive
          await supabaseClient
            .from('user_devices')
            .update({ is_active: false })
            .eq('device_token', notificationData.device_token)
          
          console.log('Marked invalid device token as inactive:', notificationData.device_token)
        }
      }
      
      throw new Error(`FCM notification failed: ${fcmResult.results[0]?.error || 'Unknown error'}`)
    }

    // Log successful notification
    console.log('FCM notification sent successfully:', {
      device_token: notificationData.device_token.substring(0, 20) + '...',
      title: notificationData.title,
      success: fcmResult.success,
      multicast_id: fcmResult.multicast_id,
    })

    // Update device last_seen timestamp
    await supabaseClient
      .from('user_devices')
      .update({ last_seen: new Date().toISOString() })
      .eq('device_token', notificationData.device_token)

    return new Response(
      JSON.stringify({
        success: true,
        message: 'Notification sent successfully',
        fcm_response: {
          success: fcmResult.success,
          failure: fcmResult.failure,
          multicast_id: fcmResult.multicast_id,
        },
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 200,
      }
    )

  } catch (error) {
    console.error('Error sending push notification:', error)
    
    return new Response(
      JSON.stringify({
        success: false,
        error: error.message,
      }),
      {
        headers: { ...corsHeaders, 'Content-Type': 'application/json' },
        status: 400,
      }
    )
  }
})
